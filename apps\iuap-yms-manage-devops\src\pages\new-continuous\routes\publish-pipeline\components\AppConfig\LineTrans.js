import { success, warn } from "iuap-gp-ymscloudui-fe/components";
import { Button, Form, Icon } from 'iuap-gp-ymscloudui-fe/baseui';
import { transferPipeline } from 'newDevopsService/NewService';
import React, { useState, } from "react";
import UserModal from "../../../../components/UserModal";
import "./LineTrans.less";

const FormItem = Form.Item;

const LineTrans = (props, ref) => {
  const [userList, setUserList] = useState([]);
  const [showData, setShowData] = useState([]);
  const [showUserModal, setShowUserModal] = useState(false);

  // 重构数组
  const rebuildApprover = (value, type) => {
    let dataArr = []
    value.map((item) => {
      dataArr.push(item[type])
    })
    return dataArr
  }

  const handleModal = (type) => () => {
    setShowUserModal(type)
  }

  const handleAdd = (list) => {
    if (userList.find(it => it.userId == list[0].userId)) {
      return warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81767", "该审批人已添加！") /* "该审批人已添加！" */);
    } else {
      setUserList(userList.concat(list))
    }
  }

  const sureTrans = async () => {
    let { baseData, getMainById, onClose } = props

    if (userList.length == 0) {
      return warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81766", "当前没有转移目标，请添加转移目标！") /* "当前没有转移目标，请添加转移目标！" */)
    }
    let params = {
      fromUserId: baseData.userId,
      fromUserName: baseData.userName,
      toUserId: userList[0].userId,
      toUserName: userList[0].userName,
      appCode: baseData.pipelineCode
    }
    let result = await transferPipeline(params)
    success(result || lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C8176A", "转移成功") /* "转移成功" */)
    getMainById(baseData.devopsPipelineMainId)
    onClose()
  }

  const delUser = (userid) => () => {
    let newList = userList.filter(it => it.userId != userid)
    setUserList(newList)
  }

  let { baseData } = props
  return (
    <div className="line_trans">
      <Form
        layout="inline"
      >
        <FormItem label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81769", "流水线创建人:") /* "流水线创建人:" */} style={{ width: 300 }}>
          <span>{baseData.userName}</span>
        </FormItem>
        <FormItem label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81764", "转移目标:") /* "转移目标:" */} style={{ width: 300 }}>
          <div className="user-list">
            {
              userList && userList.length ? userList.map((item) => (
                <span className="user-item" key={item.id}>
                  {item.name}
                  {
                    <Icon fieldid="newFlow_PnWyhRYf7z_expansion" onClick={delUser(item.userId)} type="uf-close-c" />
                  }
                </span>
              )) : (
                <Button fieldid="newFlow_lotwfKhb2k_btn" onClick={handleModal(true)}>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81768", "选择目标转移") /* "选择目标转移" */}</Button>
              )
            }
          </div>
        </FormItem>
        <FormItem >
          <Button fieldid="newFlow_b9s8x246CL_btn" onClick={sureTrans}>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C81765", "转移") /* "转移" */}</Button>
        </FormItem>
      </Form>

      <UserModal
        onClose={handleModal(false)}
        onEnsure={handleAdd}
        show={showUserModal}
        userData={showData}
      />
    </div>
  );
};

export default LineTrans;
