import React, { Component } from 'react'
import { getAssemblyLineAppId } from 'newDevopsService/NewService';
import { formateDate, getBaseURL, getCookie, transToQuery } from 'iuap-gp-ymscloudui-fe/sdk'
import { AsyncComponent } from 'iuap-gp-ymscloudui-fe/components'
import { MENU_CODE, openTopPortalTab } from 'iuap-gp-ymscloudui-fe/sdk/Diwork'
import { getDatacenterDc, getrspEnv } from 'iuap-gp-ymscloudui-fe/services'
import { err, success, warn } from 'iuap-gp-ymscloudui-fe/components'
import { Breadcrumb, Button, Dropdown, Icon, Menu, Message, Select, Modal } from 'iuap-gp-ymscloudui-fe/baseui'
import classNames from 'classnames'
import cloneDeep from 'lodash/cloneDeep'
import { inject, observer } from 'mobx-react'
import {
	checkDevops,
	checkOnlineMapStatus,
	deleteDevopsRecord,
	executeDevops,
	getMainById,
	getProductLine,
	getProjectByPipeline,
	getRecordList,
	getTemplate,
	queryAuthEnv,
	runCancelled,
	usedCheck,
	syncCurrentUserPipelinePermissions,
	getPipelineReport,
	getNodeEditable,
	getAllPackages,
	getDevopsType
} from 'newDevopsService/NewService'
import { getUserLabels } from '@yds/service/com/yonyou/iuap/ymscloud/pipeline/devops/service/IDevopsLabelService';
import { parseQuery } from 'utils'
import Socket from './webSocket'
import { withRouter } from 'iuap-gp-ymscloudui-fe/sdk/Router';
import { StoreContext } from '@/stores-new';
import { SYSMANAGE_WEB, WEBHOME_WEB, YMS_MANAGE_WEB } from "iuap-gp-ymscloudui-fe/sdk/PubConstant"
import './PublishPipeline.less'
import { isPrivateEnv } from "@/constants";
const TempateList = AsyncComponent(() => import('./components/TempateList'))
const ExecutionHistory = AsyncComponent(() => import('./containers/ExecutionHistory'))
const Latest = AsyncComponent(() => import('./containers/Latest'))
const { Item } = Breadcrumb
const EmptyTemplate = AsyncComponent(() => import('./components/EmptyTemplate'))
const AppConfig = AsyncComponent(() => import('./components/AppConfig'))
const PushImageModal = AsyncComponent(() => import('./components/ShowImage'))
const ChooseAppModal = AsyncComponent(() => import('./components/ChooseAppModal'))
const Option = Select.Option

@withRouter
@inject('CreateProcessStore')
@observer
class PublishContainers extends Component {
	static contextType = StoreContext;
	constructor() {
		super()
		this.state = {
			activeType: '',
			recordDetail: {}, // 执行记录详情
			recordId: null, // 执行记录id
			recordBuildVersion: null, // 执行记录版本//-1:展示复用页面；0:展示模板页面，其他：展示记录页面
			devopsPipelineMainId: null, // 流水线id
			recordVersionList: [], // 构建记录列表
			mainDetail: {}, // 流水线详情信息
			productObj: {}, // 产品产品线信息
			dcId: null,
			env: null,
			dcList: [],
			envList: [],
			baseId: '',
			templateData: null, // 模板数据
			allEnvList: [], // 未过滤数据中心的所有环境
			canExec: true, // 是否可以执行流水线
			// 值为false时，不显示发起上线和上线地图。 true时则显示
			controlFlag: false,
			// 值为false时，可以发起上线， 为true时，不能发起上线
			onlineFlag: false,
			// 管控环境id
			startEnvId: '',
			showPushImageModal: false, // 控制推送镜像modal
			showAppConfigModal: false, // 控制modal
			showMoveLineModal: false, // 控制迁移流水线modal
			showTemplateList: false, // 控制模版列表modal
			isAdmin: getCookie('userId') == getCookie('u_providerid'),
			adminEnvList: [], // 管理权限的环境
			markList: [], // 所有标签
			devopsLabels: [], // 流水线的标签
			showNPMModal: false, // 前端缓存modal
			userRoleCode: sessionStorage.getItem('userRoleCode'),
			cookieUserId: getCookie('userId'),
			showChooseAppModal: false, // 控制选择应用modal
			chooseAppInfo: {}, // 选择应用信息
			microServiceDevopsType: null
		}
		this.socket = null
	}

	getAll = async dcId => {
		const res2 = await this.getDatacenterList(true, dcId)
		this.queryAuthEnv(dcId || res2[0].id, true)
	}

	// 获取所有标签
	getMarkList = async () => {

		let data = await getUserLabels()
		let { devopsLabels } = this.state.mainDetail
		let newList = devopsLabels
		let list = Array.isArray(data) ? data : []
		this.setState({
			markList: list,
			devopsLabels: newList
		})
	}

	async componentDidMount() {
		let {
			match: {
				params: { id }
			},
			location: { search }
		} = this.props
		let { recordBuildVersion, env, dcId } = parseQuery(search)
		this.setState({
			recordBuildVersion: recordBuildVersion,
			devopsPipelineMainId: id,
			// recordId: recordId == 'null' ? null : recordId,
			activeType: 'latest',
			env: env,
			dcId
		})
		await this.getAllEnvList(dcId)
	}

	// 获取所有环境信息 todo 必须重构，逻辑乱得很
	getAllEnvList = async (dcId) => {
		let data = await getrspEnv()
		this.setState({
			allEnvList: Array.isArray(data) ? data : []
		}, () => {
			let temp = this.state.allEnvList.filter(item => item.Id == this.state.env)[0]
			let { updateQueryData } = this.props.CreateProcessStore;
			// 流水线未选中环境的时候，默认是开发环境，所以选用开发环境的envcode
			updateQueryData({ envCode: temp?.Code || "dev" });
			this.getAll(dcId)
		})
		return data
	}

	getDevopsType = async (id, code) => {
		if (!id || !code) return
		const res = await getDevopsType({ devopsPipelineMainId: id, pipelineCode: code })
		if (res) {
			this.setState({
				microServiceDevopsType: res
			})
		}
	}

	// 获取流水线主信息
	getMainById = async (id, env) => {
		let { updateMainData } = this.props.CreateProcessStore
		if (id === 'new') return;
		let data = await getMainById({ devopsPipelineMainId: id })
		updateMainData(data)
		this.setState(
			{
				mainDetail: data,
				baseId: data.baseId,
			},
			() => {
				this.getMarkList()
				// 获取流水线的按钮是否可用
				this.getBtnAuth()
				this.getDevopsType(id, data.pipelineCode)

				// 权限同步到微前端
				let isSync = false;
				(data.devopsLabels || []).map(item => {
					if (item.labelCode === 'FE-STATIC-DEPLOY') { // 前端静态资源部署
						isSync = true
					}
				})
				isSync && this.syncPermissions()
			}
		)
		if (data.baseId) {
			// 获取产品线、领域云、领域
			getProductLine(data.baseId).then(data => {
				this.setState({
					productObj: data
				})
			})
		}
		// 校验是否可以执行流水线
		this.validCanExec(env, id)
		// 校验是否显示发起上线和上线地图
		this.checkOnlineMapStatus(id)
	}

	syncPermissions = () => {
		try {
			const { baseId, devopsPipelineMainId: pipelineId } = this.state.mainDetail
			if (baseId) {
				syncCurrentUserPipelinePermissions({ baseId, pipelineId })
			} else {
				console.log('baseId is null not sync permissions')
			}
		} catch (e) { }
	}

	// 执行记录列表
	getRecordList = async (param, openSocketFlag) => {
		let data = await getRecordList(param)

		this.setState({
			recordVersionList: Array.isArray(data) ? data : []
		})
		// 针对路由中没有执行记录id的情况，执行记录id选最新的
		if (openSocketFlag && data.length) {
			this.setState(
				{
					recordId: data[0].devopsPipelineRecordId,
					recordBuildVersion: data[0].buildVersion,
				},
				() => {
					// 建立websocket，获取执行状态
					this.socketInit(this.state.recordId)
				}
			)
		} else {
			!data.length && this.getTemplate(param.devopsPipelineMainId, param.env)
		}
	}

	// 获取模板列表和详情
	getTemplate = async (id, env) => {
		let result = await getTemplate({
			devopsPipelineMainId: id,
			env,
			datacenterId: this.state.dcId
		})
		let data = result
		this.setState({
			recordBuildVersion: data.length > 0 ? 0 : -1
			//   templateData:data.stage
		})
	}

	componentWillUnmount() {
		this.closeSocket()
	}

	closeSocket = () => {
		this.socket && this.socket.onclose()
		clearInterval(this.taskRemindInterval)
	}

	socketInit = id => {
		if (!id) return
		this.closeSocket()
		let hostString = cloneDeep(window.location.host)
		let originString = cloneDeep(window.location.origin)
		let pattern = /^(https?:\/\/)?(\[[^\]]+\]|[\w.-]+)(:[0-9]+)?/;
		let axiosBaseURL = getBaseURL()
		if (axiosBaseURL && axiosBaseURL.match(pattern)) {
			originString = originString.replace(pattern, axiosBaseURL)
		}
		const match = originString.match(pattern)
		if (match) {
			hostString = `${match[2]}${match[3] ? match[3] : ''}`
		}

		let protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
		if (window.location.host == 'gx.yonyoucloud.com:3000' || window.location.host == '127.0.0.1:3000' || process.env.NODE_ENV == 'development') {
			protocol = 'wss:'
			hostString = 'bip-daily.yonyoucloud.com' // 开发环境不代理ws请求
		}
		this.socket = new Socket({
			socketUrl: `${protocol}//${hostString}/iuap-yms-manage/web/pipelineStatus/${id}`,
			timeout: 5000,
			socketMessage: receive => {
				this.setState({
					recordDetail: receive
				})
				if ([3, -1, -2].includes(receive.execStatus)) {
					// 当前流水线为 完成、失败、取消状态时关闭连接
					this.socket.onclose()
					clearInterval(this.taskRemindInterval)
				}
			},
			socketClose: msg => {
				msg &&
					console.log(
						lang.templateByUuid(
							'UID:P_GPAASDEVOPS-FE_17EC411004C817B0',
							lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C817B0", "状态：关闭连接") /* "状态：关闭连接" */
						) /* "状态：关闭连接" */,
						msg
					)
			},
			socketError: () => {
				console.log(
					lang.templateByUuid('UID:P_GPAASDEVOPS-FE_17EC411004C817B2', '状态') /* "状态" */ +
					lang.templateByUuid(
						'UID:P_GPAASDEVOPS-FE_17EC411004C817B3',
						lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C817B3", "连接建立失败") /* "连接建立失败" */
					) /* "连接建立失败" */
				)
			},
			socketOpen: () => {
				console.log(
					lang.templateByUuid('UID:P_GPAASDEVOPS-FE_17EC411004C817B8', '连接建立成功') /* "连接建立成功" */
				)
				// 心跳机制 定时向后端发数据,防止断开
				this.taskRemindInterval = setInterval(() => {
					if (this.socket?.socket?.readyState === 3) {
						clearInterval(this.taskRemindInterval);
					} else if (this.socket?.socket?.readyState === 1) {
						this.socket.sendMessage({ msgType: 0 })
					}
				}, 50000)
			}
		}) // 重试创建socket连接
		try {
			this.socket.connection()
		} catch (e) {
			// 捕获异常，防止js error
		}
	}

	getDatacenterList = async (isFirst, dcId) => {
		const data = await getDatacenterDc()
		if (data && data.length) {
			this.setState({
				dcList: data || [],
				dcId: dcId || (data ? data[0].id : '')
			})
		}
		return data
	}

	// 获取环境列表
	queryAuthEnv = async (dcId, isFirst) => {
		let {
			match: {
				params: { id }
			}
		} = this.props
		let { allEnvList, isAdmin } = this.state
		const data = await queryAuthEnv({
			datacenterId: dcId,
			mainId: id
		})
		let authEnvs = data
		let envList = []
		let adminEnvList = [] // 有管理权限的环境，在权限管理出授权用
		data.forEach(it => {
			let obj = allEnvList.find(item => item.Id == it.env)
			if (obj) {
				envList.push(obj)
			}
		})
		if (!isAdmin) {
			// 如果不是管理员，最终的环境 取的是 allEnvList和authEnvs的交集
			authEnvs &&
				authEnvs.length &&
				authEnvs.forEach(item2 => {
					allEnvList.forEach(item1 => {
						if (item1.Id == item2.env) {
							let obj = item1
							obj.auth = item2.auth
							if (item2.auth == 'owner') {
								adminEnvList.push(obj)
							}
							return
						}
					})
				})
		}
		let env = ''
		if (isFirst) {
			env = this.state.env || data[0].env
			this.getRecordList(
				{
					devopsPipelineMainId: this.state.devopsPipelineMainId,
					env: env
				},
				true
			)
		} else {
			env = (data && data[0] && data[0].env) || ''
			if (env) {
				this.getRecordList(
					{
						devopsPipelineMainId: this.state.devopsPipelineMainId,
						env: env
					},
					true
				)
				this.validCanExec(env, id)
			} else {
				this.setState({
					recordDetail: {},
					recordBuildVersion: -1
				})
			}
		}
		this.setState({
			envList,
			env: env,
			adminEnvList
		}, () => {
			const { Code } = envList.filter(item => item.Id == env)[0] || {};
			Code && this.props.CreateProcessStore.updateQueryData({ envCode: Code });
			this.getMainById(id, env)
		})
	}

	// 控制模态框显示
	controlModal = type => value => {
		this.setState({
			[type]: value
		})
	}

	// 校验是否存在云机一体使用记录
	checkCloud = () => {
		let { mainDetail } = this.state
		let params = {
			pipelineCode: mainDetail.pipelineCode,
			env: 'test'
		}
		return usedCheck(params)
	}

	openTemplateListModal = type => {
		this.setState({
			showTemplateList: true,
			openType: type
		})
	}

	closeTemplateListModal = () => {
		this.setState({
			showTemplateList: false,
			openType: ''
		})
	}

	changeType = type => () => {
		if (type != this.state.activeType) {
			this.setState({
				activeType: type
			})
		}
		if (type == 'latest') {
			this.setState({
				envList: this.state.envList.filter(it => it.Id != 'all')
			})
		}
	}

	// 切换流水号
	changeReordId = ({ devopsPipelineRecordId, buildVersion }) => {
		// if(buildVersion == this.state.recordBuildVersion)return
		this.setState(
			{
				recordId: devopsPipelineRecordId,
				recordBuildVersion: buildVersion,
				activeType: 'latest'
			},
			() => {
				this.freshRouter()
				this.socketInit(this.state.recordId)
			}
		)
	}

	onSelectRecord = value => {
		let { recordVersionList } = this.state
		let findObj = recordVersionList.find(it => it.devopsPipelineRecordId == value)
		let obj = {
			devopsPipelineRecordId: value,
			buildVersion: findObj.buildVersion
		}
		this.changeReordId(obj)
	}

	// 校验是否可以执行流水线
	validCanExec = async (env, pipelineId) => {
		if (!pipelineId || !env) return
		let param = {
			pipelineId,
			env
		}
		let data = await checkDevops(param).catch(error => console.log(error))
		this.setState({
			canExec: !!data
		})
		return data
	}
	// 校验是否显示发起上线和上线地图
	checkOnlineMapStatus = async pipelineId => {
		if (!pipelineId) return
		let param = {
			pipelineId
		}
		let data = await checkOnlineMapStatus(param).catch(error => console.log(error))
		this.setState({
			controlFlag: data.controlFlag === 'true',
			onlineFlag: data.onlineFlag === 'true',
			startEnvId: data.startEnvId
		})
		return data
	}

	startOnline = async () => {
		let { mainDetail, env, devopsPipelineMainId, recordId, startEnvId } = this.state
		this.checkOnlineMapStatus(devopsPipelineMainId).then(res => {
			let notice1 = lang.templateByUuid('UID:P_GPAASDEVOPS-FE_17EC411004C817B4', '当前环境无法发起上线,请选择其他环境')
			let notice2 = lang.templateByUuid('UID:P_GPAASDEVOPS-FE_17EC411004C817B6', '流水线', undefined, { returnStr: true }) /* "流水线" */
			let notice3 = lang.templateByUuid(
				'UID:P_GPAASDEVOPS-FE_17EC411004C817B5',
				lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C817B5", "正在上线中,请勿重复提交") /* "正在上线中,请勿重复提交" */,
				undefined,
				{ returnStr: true }
			) /* "正在上线中,请勿重复提交" */

			if (env === 'all')
				return warn(notice1)
			if (res.onlineFlag)
				return warn(
					`${notice2}${mainDetail.pipelineName}${notice3}`
				)
			this.validCanExec().then(res => {
				if (res)
					return Message.success({
						content: lang.templateByUuid(
							'UID:P_GPAASDEVOPS-FE_17EC411004C817B9',
							lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C817B9", "不在锁定时间内，可正常执行流水线") /* "不在锁定时间内，可正常执行流水线" */
						) /* "不在锁定时间内，可正常执行流水线" */,
						duration: 5
					})
				this.props.navigate(
					`/new-continuous/start-online?devopsPipelineMainId=${devopsPipelineMainId}&env=${startEnvId || env
					}&pipelineDetailId=${recordId}&pipelineName=${mainDetail.pipelineName}`
				)
			})
		})
	}

	onlineMap = () => {
		let { devopsPipelineMainId, dcId, envList } = this.state
		let authEnvData = {
			[devopsPipelineMainId]: envList.map(({ Id }) => Id)
		}
		sessionStorage.setItem('authEnvIds', JSON.stringify(authEnvData))
		this.props.navigate(`/new-continuous/online-map/${devopsPipelineMainId}?dcId=${dcId}`)
	}

	// 执行流水线
	executeDevops = async (templateId, reason, buildPatch) => {
		let { devopsPipelineMainId, env } = this.state
		let obj = {
			devopsPipelineMainId,
			env,
			templateId
		}
		let postData;
		if (typeof reason != undefined) {
			obj.reason = reason
		}
		if (typeof buildPatch != undefined) {
			postData = buildPatch
		}
		let data = await executeDevops(obj, postData)
		this.getRecordList({ devopsPipelineMainId, env })
		this.changeReordId(data)
	}

	// 删除流水线记录
	deleteDevopsRecord = async () => {
		Modal.confirm({
			title: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C817A3", "确认要删除本条流水线记录吗") /* "确认要删除本条流水线记录吗" */,
			onOk: async () => {
				try {
					let { recordId, devopsPipelineMainId, env } = this.state
					await deleteDevopsRecord({ recordId })
					success(lang.templateByUuid('UID:P_GPAASDEVOPS-FE_17EC411004C817A4', '删除成功') /* "删除成功" */)
					let data = await getRecordList({ devopsPipelineMainId, env })
					this.setState(
						{
							recordVersionList: Array.isArray(data) ? data : []
						},
						() => {
							if (this.state.recordVersionList.length > 0) {
								// 删除后选择最新的流水线记录
								this.onSelectRecord(this.state.recordVersionList[0].devopsPipelineRecordId)
							} else if (this.state.recordVersionList.length == 0) {
								this.setState({
									recordId: '',
									activeType: '',
									recordBuildVersion: 0
								})
							}
						}
					)
				} catch (e) {
					console.log(e)
				}
			}
		})
	}

	// 暂停正在执行的流水线
	stopDevops = async () => {
		let { recordId } = this.state
		runCancelled({ recordId }).then(() => {
			success(lang.templateByUuid('UID:P_GPAASDEVOPS-FE_17EC411004C817B1', '取消成功') /* "取消成功" */)
		})
	}

	openReport = async () => {
		let { recordId } = this.state
		await getPipelineReport({ pipelineRecordId: recordId })
	}

	handleSelect = type => async value => {
		if (type == "env") {
			let temp = this.state.envList.filter(item => item.Id == value)[0]
			let { updateQueryData } = this.props.CreateProcessStore;
			// 流水线未选中环境的时候，默认是开发环境，所以选用开发环境的envcode
			updateQueryData({ envCode: temp?.Code || "dev" })
		}
		this.setState(
			{
				[type]: value,
				recordId: null
			},
			() => {
				if (type == 'dcId') {
					this.queryAuthEnv(value)
				} else {
					this.getRecordList({ devopsPipelineMainId: this.state.devopsPipelineMainId, env: value }, true)
					this.validCanExec(value, this.state.devopsPipelineMainId)
					this.getBtnAuth()
				}
				this.freshRouter()
			}
		)
	}

	freshRouter = () => {
		let { recordBuildVersion, env, dcId, recordId, devopsPipelineMainId } = this.state
		let params = {
			dcId,
			env,
			recordBuildVersion: recordBuildVersion,
			recordId
		}
		let search = ''
		Object.keys(params).forEach((item, i) => {
			i === 0 ? (search += `?${item}=${params[item]}`) : (search += `&${item}=${params[item]}`)
		})
		this.props.navigate({
			pathname: '/new-continuous/pipeline-detail/' + devopsPipelineMainId,
			search
		})
	}

	// 重构数组
	rebuildArr = value => {
		let dataArr = []
		value.map(item => {
			dataArr.push(item.pipelineTemplate)
		})
		return dataArr
	}

	postTabMessage = () => {
		if (window.jDiwork) {
			console.log('postMessage', '================')
			window.jDiwork.closeWin('gPaaS_' + MENU_CODE.ymsMgr)
		}
	}

	/*
	 * 微服务管理的跳转
	 */
	goToApp = () => {
		const { env, mainDetail, envList, dcId } = this.state
		// if (recordId) {// 存在选中的流水线
		let totalEnvObj = envList.filter(item => item.Id == env)[0]
		if (!totalEnvObj) {
			return err(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B80438009C", "缺少环境对象") /* "缺少环境对象" */)
		}
		getAssemblyLineAppId(mainDetail.pipelineCode, totalEnvObj.Code, mainDetail.devopsType)
			.then(data => {
				if (mainDetail.devopsType === "jq" && data?.jumpAppInfoList?.length) { // 简强类型流水线，并且有可选流水线，则打开弹窗让用户选择
					this.setState({
						showChooseAppModal: true,
						chooseAppInfo: {
							...data,
							dcId,
							code: totalEnvObj.Code,
							devopsPipelineMainId: mainDetail.devopsPipelineMainId
						},
					})
				} else if ((data && data.appId) || (mainDetail.devopsType !== "jq" && data?.jumpAppInfoList?.length)) {
					// 兼容非简强类型但是返回可选项的情况，可能需要后端改
					let id = data.appId
					if ((mainDetail.devopsType !== "jq" && data?.jumpAppInfoList?.length)) {
						id = data.jumpAppInfoList[0].appId || ""
					}
					const { pipelineCode, pipelineName } = mainDetail;
					let detailUrl = `/app-manager/service-detail/${id}?app_type=${totalEnvObj.Code}&microServiceName=${encodeURI(pipelineName || '')}&microServiceCode=${pipelineCode || ''}&router=/cv_detail/${mainDetail.devopsPipelineMainId}&from=continuous&selectDc=${dcId}&activeCode=debug`
					const para = {
						url: window.origin + `${YMS_MANAGE_WEB}/#` + detailUrl,
						multiTitle: {
							zh_CN: "微服务详情",//@notranslate
							en_US: "Microservice Details",
							zh_TW: "微服務詳情",//@notranslate
							id_ID: "Detail Layanan Mikro"
						},
						id: MENU_CODE.ymsMgr,
						code: 'new_appManager_detail',
						isFresh: true,
						serviceType: 'tns',
						providerEntry: 'microservice',
						routePath: detailUrl,
						moduleType: 'main'
					}
					// this.postTabMessage()
					openTopPortalTab(para)
				} else {
					return warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B00528008F", "微服务不存在或未部署完成") /* "微服务不存在或未部署完成" */)
				}
			})
			.catch(err => {
				console.log(err)
				warn(lang.templateByUuid('UID:P_GPAASDEVOPS-FE_17EC411004C817C1', '跳转出错！') /* "跳转出错！" */)
			})
		// } else {
		// warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C817C0","应用不存在或未部署完成") /* "应用不存在或未部署完成" */)
		// }
	}
	/**
	 * 跳转到二方包详情页面 根据二方包列表来查，如果extData4匹配上 则可以跳转
	 * modify by ligaoming for QDJCJS-25557 at 2021-12-03
	 * @returns {Promise<void>}
	 */
	goToWebHome = async () => {
		getAllPackages({
			tenantId: getCookie('u_providerid'),
			types: 2
		}).then((res) => {
			const { mainDetail, env, allEnvList } = this.state;
			const currentPackage = res.find(item => item.extData4 === mainDetail.pipelineCode);
			if (!currentPackage) {
				warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B005280092", "未找到二方包信息！") /* "未找到二方包信息！" */)
				return;
			}
			const currentEnv = allEnvList.find(it => it.Id == env);
			const { Name: envName, Code: envCode } = currentEnv || {}
			const queryParams = transToQuery({
				env: envCode,
				name: currentPackage.code
			});
			const url = `/ynpm-manage/package-detail${queryParams}`;
			const obj = {
				url: window.origin + `${WEBHOME_WEB}/#${url}`,
				title: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B005280091", "前端二方包详情") /* "前端二方包详情" */,
				id: 'ynpm-manage',
				code: 'package-detail',
				serviceType: 'tns',
				providerEntry: 'webhome',
				routePath: url,
				moduleType: 'webhome',
			};
			openTopPortalTab(obj);
		});
	};
	goToFE = async () => {
		const { env, mainDetail, envList, microServiceDevopsType } = this.state
		let code = ""
		if (microServiceDevopsType?.length) {
			code = microServiceDevopsType[0]?.code
		} else {
			const projectInfo = await getProjectByPipeline([mainDetail.devopsPipelineMainId])
			if (projectInfo.success) {
				const result = projectInfo.backData
				if (result.length > 0) {
					code = result[0].projectCode;
				}
				if (result.length > 1) {
					return warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1AA6E4B605600092", "该流水线绑定了多个微前端，请联系管理员处理") /* "该流水线绑定了多个微前端，请联系管理员处理" */)
				}
				if (result.length === 0) {
					return warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1B0548CC05D000D4", "没找到该流水线绑定的微前端") /* "没找到该流水线绑定的微前端" */)
				}
			} else {
				return err(projectInfo.backMsg)
			}
		}
		if (code) {
			let totalEnvObj = envList.filter(item => item.Id == env)[0]
			let url = `/micro-front-end-management/resource-manager/${code}?env=${totalEnvObj.Code}`
			let obj = {
				url: window.origin + `${WEBHOME_WEB}/#${url}`,
				title: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1AA6E4B605600091", "微前端详情") /* "微前端详情" */,
				id: 'micro-front-end-management',
				code: 'resource-manager',
				serviceType: 'tns',
				providerEntry: 'webhome',
				routePath: url,
				moduleType: 'webhome'
			}
			openTopPortalTab(obj)
		} else {
			return err(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1EC2E7D205C8000B", "没有获取到微前端编码") /* "没有获取到微前端编码" */)
		}
	}

	renderDeleteBtn = execStatus => {
		let { recordVersionList, env } = this.state
		if (recordVersionList && recordVersionList.length > 0) {
			return env == 'all' || [1, 5].includes(execStatus) ? null : (
				<Button
					fieldid='newFlow_iaDh7AWKmr_btn'
					style={{ marginLeft: '10px' }}
					onClick={this.deleteDevopsRecord}
				>
					{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1A13293A05D80157", "删除记录") /* "删除记录" */}
				</Button>
			)
		}
	}

	preCheck = () => {
		if (this.state.env == '5') {
			this.checkCloud().then(res => {
				console.log(res)
				if (res == true) {
					this.openTemplateListModal('exec')
				} else {
					return warn(
						lang.templateByUuid(
							'UID:P_GPAASDEVOPS-FE_17EC411004C817BA',
							lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C817BA", "执行被拒绝：未找到当前流水线在[测试环境]的云机一体使用记录！") /* "执行被拒绝：未找到当前流水线在[测试环境]的云机一体使用记录！" */
						) /* "执行被拒绝：未找到当前流水线在[测试环境]的云机一体使用记录！" */
					)
				}
			})
		} else {
			this.openTemplateListModal('exec')
		}
	}

	// 节点可编辑
	getBtnAuth = async () => {
		const { handleCurrentNode } = this.props.CreateProcessStore
		const { baseId, env } = this.state;
		if (!baseId) {
			handleCurrentNode({ disable_node: false }) // 未调用接口不禁用按钮
			return
		}
		const params = {
			baseId: baseId,
			envId: env,
			authType: 'node'
		}
		const data = await getNodeEditable(params)
		handleCurrentNode({ disable_node: !data })
	}

	render() {
		console.log(this.context, 'xxxxxxxxxxxxxxxx')

		let {
			activeType,
			recordDetail,
			recordBuildVersion,
			devopsPipelineMainId,
			recordVersionList,
			mainDetail,
			recordId,
			productObj,
			allEnvList,
			adminEnvList,
			isAdmin,
			canExec,
			controlFlag,
			dcList,
			envList,
			env,
			dcId,
			showChooseAppModal,
			chooseAppInfo,
			microServiceDevopsType
		} = this.state
		const currentEnv = allEnvList.find(it => it.Id == env)
		const { Name: envName, Code: envCode } = currentEnv || {}
		let appName = mainDetail.pipelineName
		let appCode = mainDetail.pipelineCode
		let isFE = false;
		let isTne2nd = false; // 是否跳转到ynpm组件详情页面 判断逻辑为以tne2nd开头的标签

		console.log(mainDetail.devopsPipelineMainId);
		// modify by ligaoming for QDJCJS-25557 at 2021-12-02
		if (microServiceDevopsType?.length) {
			const type = microServiceDevopsType[0]?.type
			isFE = ['isFe', 'isYnf'].includes(type)
			isTne2nd = type === '2'
		} else {
			const devopsLabels = mainDetail.devopsLabels || [];
			for (let i = 0; i < devopsLabels.length; i++) {
				const item = devopsLabels[i];
				if (item.labelName?.toLocaleLowerCase()?.includes('tne2nd')) {
					isTne2nd = true;
					break;
				} else if (item.labelCode === 'FE-STATIC-DEPLOY') {
					isFE = true;
					break;
				}
			}
		}

		recordBuildVersion = Number(recordBuildVersion)
		let { currentNodeInfo } = this.props.CreateProcessStore;
		let { disable_node } = currentNodeInfo
		return (
			<div className='publish-pipeline-containers'>
				<div className='title-back-header'>
					<div className='header-bread'>
						<Breadcrumb separator='>' fieldid='newFlow_5apn6dXIYE_expansion' onClick={this.handleClick}>
							{
								productObj.undocumented ?
									<Item separator=''>
										<span title={productObj.undocumented}>{productObj.undocumented}</span>
									</Item> :
									[
										<Item>
											<span className='label'>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1B0548CC05D0003D", "产品") /* "产品" */}：</span>
											<span className='value' title={productObj?.product_category}>{productObj?.product_category}</span>
										</Item>,
										<Item>
											<span className='label'>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C80694", "领域") /* "领域" */}：</span>
											<span className='value' title={productObj?.domain_cloud}>{productObj?.domain_cloud}</span>
										</Item>,
										<Item separator={isPrivateEnv ? '' : '>'}>
											<span className='label'>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1BA955340480016D", "子领域") /* "子领域" */}：</span>
											<span className='value' title={productObj?.domain}>{productObj?.domain}</span>
										</Item>,
										isPrivateEnv ? null : (
											<Item separator=''>
												<span className='label'>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1A13293A05D80198", "所属应用") /* "所属应用" */}：</span>
												<span className='value w400' title={productObj?.product}>{productObj?.product}</span>
											</Item>
										)
									]
							}
						</Breadcrumb>
					</div>

					<div className='sider-btns'>
						<Dropdown.Button
							trigger="click"
							style={{ width: 70, display: 'none' }}
							overlay={
								<Menu>
									<Menu.Item key="GitRunner">GitRunner</Menu.Item>
									<Menu.Item key="YMAL">{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1B6A1BEC05A0011E", "YMAL文件校验") /* "YMAL文件校验" */}</Menu.Item>
								</Menu>
							}
						>
							{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1B6A1BEC05A0011F", "高级") /* "高级" */}
						</Dropdown.Button>

						{mainDetail.devopsType !== 'jq' && (
							<Button
								icon={<Icon type='uf-send' />}
								fieldid='newFlow_hgRc3V69pF_expansion'
								onClick={this.controlModal('showPushImageModal', true)}
								disabled={disable_node}
							>
								{
									lang.templateByUuid(
										'UID:P_GPAASDEVOPS-FE_17EC411004C817BC',
										lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C817BC", "推送镜像") /* "推送镜像" */
									) /* "推送镜像" */
								}
							</Button>
						)}
						{
							console.log(disable_node)
						}
						<Button
							icon={<Icon type='uf-play-o' />}
							colors='primary'
							disabled={disable_node || env == 'all' || !canExec}
							fieldid='newFlow_GDJWouxgt1_expansion'
							onClick={this.preCheck}
						>
							{
								lang.templateByUuid(
									'UID:P_GPAASDEVOPS-FE_17EC411004C817BF',
									lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C817BF", "执行流水线") /* "执行流水线" */
								) /* "执行流水线" */
							}
						</Button>
						{controlFlag && (
							<>
								<Button
									icon={<Icon type='uf-play-o' />}
									// colors="primary"
									// disabled={onlineFlag}
									fieldid='newFlow_pipeline_online_start'
									onClick={this.startOnline}
								>
									{
										lang.templateByUuid(
											'UID:P_GPAASDEVOPS-FE_18F928C405080090',
											lang.templateByUuid("UID:P_GPAASDEVOPS-FE_18F928C405080090", "发起上线") /* "发起上线" */
										) /* "发起上线" */
									}
								</Button>
								<Button
									icon={<Icon type='uf-listwithdots' />}
									// colors="primary"
									fieldid='newFlow_pipeline_online_map'
									onClick={this.onlineMap}
								>
									{
										lang.templateByUuid(
											'UID:P_GPAASDEVOPS-FE_18F928C405080091',
											lang.templateByUuid("UID:P_GPAASDEVOPS-FE_18F928C405080091", "上线地图") /* "上线地图" */
										) /* "上线地图" */
									}
								</Button>
							</>
						)}
						<Button
							fieldid='dropdown2'
							onClick={this.controlModal('showAppConfigModal', true)}
							icon={<Icon type='uf-settings' />}
						>
							{lang.templateByUuid('UID:P_GPAASDEVOPS-FE_17EC411004C817A7', '应用配置') /* "应用配置" */}
						</Button>
						<Button fieldid="yks_wu7Qj63h4u_expansion" onClick={() => {
							const { devopsPipelineMainId, pipelineName } = mainDetail;
							const url = `/change-details-list?resId=${devopsPipelineMainId}&typeGroup=PIPELINE&keyword=${encodeURIComponent(pipelineName)}&dataCenterId=${dcId}&env=${env}`;
							const obj = {
								url: window.origin + `${SYSMANAGE_WEB}/#${url}`,
								title: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1C3715E804880057", "变更大盘") /* "变更大盘" */,
								id: 'Change_Overall',
								serviceType: "tns",
								providerEntry: 'master',
								moduleType: 'master',
								routePath: url
							}
							console.log('uirs', url)
							openTopPortalTab(obj);
						}}>
							{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1C3715E804880057", "变更大盘") /* "变更大盘" */}
						</Button>
						{env != 'all' &&
							recordBuildVersion != -1 && [
								isTne2nd
									? <Button
										icon={<Icon type='uf-2collayout' />}
										onClick={this.goToWebHome}
										fieldid='newFlow_pipeline_Microapp'
									>
										{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B005280090", "二方包详情") /* "二方包详情" */}
									</Button>
									: isFE
										? <Button
											bordered
											icon={<Icon type='uf-2collayout' />}
											onClick={() => this.goToFE()}
											fieldid='newFlow_pipeline_goto_fe'
										>
											{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1AA6E4B605600090", "微前端应用") /* "微前端应用" */}
										</Button>
										: <Button
											icon={<Icon type='uf-2collayout' />}
											onClick={this.goToApp}
											fieldid='newFlow_pipeline_Microapp'
										>
											{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C817A9", "微服务管理") /* "微服务管理" */}
										</Button>,
								<Button
									bordered
									colors='primary'
									icon={<Icon type='uf-pencil-s' />}
									onClick={() => this.openTemplateListModal('editTemplate')}
									fieldid='newFlow_pipeline_newlineEdit'
								>
									{lang.templateByUuid('UID:P_GPAASDEVOPS-FE_17EC411004C817AA', '编辑') /* "编辑" */}
								</Button>
							]}
					</div>
				</div>
				<div className='publish-pipeline-containers-headers'>
					<div className='headers'>
						<span className='header-tabs multilang'>
							<span
								fieldid='newFlow_n0mpygdvPG_expansion'
								onClick={this.changeType('history')}
								className={classNames('tab', activeType == 'history' ? 'active-tab' : '')}
							>
								{
									lang.templateByUuid(
										'UID:P_GPAASDEVOPS-FE_17EC411004C817AF',
										lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C817AF", "执行历史") /* "执行历史" */
									) /* "执行历史" */
								}
							</span>
							<span
								fieldid='newFlow_yewXOsi5Uq_expansion'
								onClick={this.changeType('latest')}
								className={classNames('tab', activeType == 'latest' ? 'active-tab' : '')}
							>
								{recordBuildVersion <= 0 ? '#' : `#${recordBuildVersion}`}
							</span>
						</span>

						<label>
							{lang.templateByUuid('UID:P_GPAASDEVOPS-FE_17EC411004C817BB', '数据中心') /* "数据中心" */}
						</label>
						<Select
							fieldid='newFlow_i5N8tFHMZK_select'
							showSearch
							onSelect={this.handleSelect('dcId')}
							value={dcId}
							optionFilterProp='children'
						>
							{dcList.map(it => (
								<Option key={it.id} value={it.id}>
									{it.dc_name}
								</Option>
							))}
						</Select>
						<label>
							{lang.templateByUuid('UID:P_GPAASDEVOPS-FE_17EC411004C817BE', '环境') /* "环境" */}
						</label>
						<Select
							fieldid='newFlow_hqwnT83ivz_select'
							showSearch
							onSelect={this.handleSelect('env')}
							value={env}
							optionFilterProp='children'
						>
							{envList.map(it => (
								<Option key={it.Id} value={it.Id}>
									{it.Name}
								</Option>
							))}
						</Select>
						<label>
							{
								lang.templateByUuid(
									'UID:P_GPAASDEVOPS-FE_17EC411004C817C2',
									lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C817C2", "当前流水线") /* "当前流水线" */
								) /* "当前流水线" */
							}
						</label>
						<Select fieldid='newFlow_PI1inMtj7w_select' value={recordId} onSelect={this.onSelectRecord}>
							{recordVersionList.map(it => {
								return (
									<Option value={it.devopsPipelineRecordId}>
										{`#${it.buildVersion} ${formateDate(it.ts)}`}
									</Option>
								)
							})}
						</Select>
					</div>
				</div>
				{activeType == 'latest' && recordBuildVersion > 0 && (
					<Latest
						mainDetail={{ ...mainDetail, appName: productObj.application, msoCode: productObj.applicationCode }}
						recordId={recordId}
						allEnvList={allEnvList}
						dcList={dcList}
						env={env}
						envCode={envCode}
						dcId={dcId}
						openReport={this.openReport}
						stopDevops={this.stopDevops}
						recordDetail={recordDetail}
						devopsLabels={this.state.devopsLabels}
						markList={this.state.markList}
						getMainById={this.getMainById}
						getUserLabels={this.getMarkList}
						renderDeleteBtn={this.renderDeleteBtn}
						socketInit={this.socketInit}
					/>
				)}
				{recordBuildVersion <= 0 && activeType && activeType != 'history' && (
					<EmptyTemplate
						devopsPipelineMainId={devopsPipelineMainId}
						env={env}
						envList={envList}
						selectDc={dcId}
						datacenterDc={dcList}
						appName={mainDetail.pipelineName}
						appCode={mainDetail.pipelineCode}
						recordBuildVersion={recordBuildVersion}
						getRecordList={this.getRecordList}
						devopsType={mainDetail.devopsType}
						openTemplateListModal={this.openTemplateListModal}
					/>
				)}
				{activeType == 'history' && (
					<ExecutionHistory
						recordDetail={recordDetail}
						onSelectRecord={this.onSelectRecord}
						envList={envList}
						env={env}
						devopsPipelineMainId={devopsPipelineMainId}
					/>
				)}
				{this.state.showAppConfigModal ? (
					<AppConfig
						show={this.state.showAppConfigModal}
						onClose={this.controlModal('showAppConfigModal', false)}
						isAdmin={isAdmin}
						isFE={isFE}
						baseData={mainDetail}
						adminEnvList={adminEnvList}
						envList={envList}
						env={this.state.env}
						devopsPipelineMainId={devopsPipelineMainId}
						getMainById={this.getMainById}
						userRoleCode={this.state.userRoleCode}
						cookieUserId={this.state.cookieUserId}
						allEnvList={allEnvList}
						dcList={dcList}
					/>
				) : null}
				{
					showChooseAppModal && <ChooseAppModal
						show={showChooseAppModal}
						data={chooseAppInfo}
						onCancel={
							() => {
								this.setState({
									showChooseAppModal: false,
									chooseAppInfo: {}
								})
							}
						}
					/>
				}
				{
					// this.state.showPushImageModal ?
					<PushImageModal
						show={this.state.showPushImageModal}
						onClose={this.controlModal('showPushImageModal', false)}
						envList={envList}
						datacenterDc={dcList}
						env={env}
						selectDc={dcId}
						baseData={mainDetail}
						devopsPipelineMainId={devopsPipelineMainId}
						AllEnvList={allEnvList}
					/>
					// :null
				}
				{this.state.showTemplateList ? (
					<TempateList
						show={this.state.showTemplateList}
						onClose={this.closeTemplateListModal}
						env={env}
						devopsPipelineMainId={devopsPipelineMainId}
						envCode={envCode}
						envName={envName}
						appName={appName}
						appCode={appCode}
						productObj={productObj}
						executeDevops={this.executeDevops}
						openType={this.state.openType}
						devopsType={mainDetail.devopsType}
						dcId={dcId}
						dcList={dcList}
						mainDetail={mainDetail}
						envList={envList}
						applicationCode={productObj.applicationCode}
					/>
				) : null}
			</div>
		)
	}
}

export default PublishContainers
