import React, { useState, useEffect, useRef, useImperativeHandle, forwardRef } from 'react'
import { Checkbox, Col, Form, Row, Select, } from 'iuap-gp-ymscloudui-fe/baseui';
import { inject, observer } from 'mobx-react';
import { getMainById } from 'newDevopsService/NewService';

const Option = Select.Option;
const FormItem = Form.Item;

import { CONF_APP_TYPE, comboLanguageTypeData } from '@/constants/newContinuous';

const Sonar = forwardRef((props, ref) => {
  const [state, setState] = useState({
    canUseCheckmax: true, // 是否可以看见checkmarx
    sonarFlag: false, // 是否开启安全扫描
    langFlag: false, // 是否开启多语言扫描
    fortifyFlag: false, // 是否开启fortify
    checkmaxFlag: false, // 是否开启checkmarx
    JobList: [],
    checkedAllFlag: false,
    pages: 0,
    jobs: [],
    dependencyCheck: false, // 是否开启二方三方包扫描
    sonarLanguageType: [CONF_APP_TYPE[props.appType]],
    checkmaxLanguageType: [CONF_APP_TYPE[props.appType]],
    langLanguageType: ["java"]
  });
  const flagRef = useRef(true);

  // 获取流水线主信息
  const getMainByIdData = async () => {
    let { baseData, updateMainData } = props.CreateProcessStore;
    if (baseData.productId) {
    } else {
      if (props.id === 'new') return;
      let data = await getMainById({ devopsPipelineMainId: props.id })
      updateMainData(data)
      if (data.groupId) {
      }
    }
  }

  /**
   * 选择是否选中
   */
  const handleCheck = field => value => {
    flagRef.current = true;
    setState(prevState => {
      const newState = { ...prevState, [field]: value };
      let { sonarLanguageType, checkmaxLanguageType, langLanguageType } = prevState;

      if (field == "sonarFlag") {
        newState.sonarLanguageType = value ? sonarLanguageType : ["java"];
      }
      if (field == "checkmaxFlag") {
        newState.checkmaxLanguageType = value ? checkmaxLanguageType : ["java"];
      }
      if (field == "allCheckType") {
        newState.checkmaxLanguageType = value ? checkmaxLanguageType : ["java"];
        if (value == false) {
          newState.checkmaxFlag = false;
        }
      }
      if (field === "langFlag") {
        newState.langLanguageType = value ? langLanguageType : ["java"];
      }

      return newState;
    });
  }

  useEffect(() => {
    let type = [CONF_APP_TYPE[props.devopsType]]
    getMainByIdData()
    let {
      sonarLanguageType,
      checkmaxLanguageType,
      fortifyFlag,
      checkmaxFlag,
      allCheckType,
      langLanguageType
    } = props.taskinfo.params || {}

    setState(prevState => ({
      ...prevState,
      ...props.taskinfo.params,
      sonarLanguageType: !sonarLanguageType ? type : sonarLanguageType.split(','),
      checkmaxLanguageType: !checkmaxLanguageType ? type : checkmaxLanguageType.split(','),
      allCheckType: checkmaxFlag || fortifyFlag || allCheckType,
      langLanguageType: !langLanguageType ? ["java"] : langLanguageType.split(','),
    }))
  }, []);

  // 选择语言类型
  const handleChange = (field) => value => {
    flagRef.current = true
    setState(prevState => ({
      ...prevState,
      [field]: value
    }))
  };

  const saveData = () => {
    let {
      sonarFlag,
      sonarLanguageType,
      checkmaxFlag,
      checkmaxLanguageType,
      allCheckType,
      langFlag,
      langLanguageType
    } = state;
    let data = {
      sonarFlag,
      sonarLanguageType: sonarLanguageType.join(','),
      checkmaxFlag,
      checkmaxLanguageType: checkmaxLanguageType.join(','),
      allCheckType,
      langFlag,
      langLanguageType: langLanguageType.join(',')
    }
    let { CreateProcessStore } = props;
    let { updateNodeInfo } = CreateProcessStore;
    updateNodeInfo(data)
  }

  const scrollToEnd = () => {
    // 滚动到底部的逻辑
  }

  // 使用 useImperativeHandle 暴露方法和属性
  useImperativeHandle(ref, () => ({
    saveData,
    flag: flagRef.current,
    state
  }));

  let { canUseCheckmax, allCheckType, sonarFlag, checkmaxFlag, langFlag, } = state;
  let { isEdit } = props;

  return (
    <div className="sonar">
      <div style={{ marginBottom: 30 }}>
        <div>
          <Row grid={12} style={{ padding: "20px 40px" }}>
            <Col lg={12} md={12} xs={12} sm={12} className="checkList">
              {
                canUseCheckmax ? (
                  <FormItem
                    className="root-checkbox-sonar"
                  >
                    <Checkbox
                      checked={checkmaxFlag}
                      disabled={!isEdit}
                      style={{ marginRight: 10 }}
                      fieldid={`canuse_checkbox`}
                      onChange={handleCheck("checkmaxFlag")}
                    >{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D052C6804580034", "启用安全扫描") /* "启用安全扫描" */}</Checkbox>
                  </FormItem>
                ) : null
              }
              {
                checkmaxFlag ?
                  <FormItem label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C80BFB", "语言类型：") /* "语言类型：" */}>
                    <Select fieldid="newFlow_BLM2nCZ7Om_select"
                      multiple
                      value={state.checkmaxLanguageType}
                      style={{ width: "200px" }}
                      searchPlaceholder={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C80BFA", "语言类型") /* "语言类型" */}
                      scrollToEnd={scrollToEnd}
                      disabled={!isEdit}
                      onChange={handleChange("checkmaxLanguageType")}
                    >
                      {
                        comboLanguageTypeData.map(item => (
                          <Option key={item.value} value={item.value} >{item.name}</Option>
                        ))
                      }
                    </Select>
                  </FormItem>
                  : null
              }
            </Col>
            <Col lg={12} md={12} xs={12} sm={12} className="checkList">
              <FormItem
                className="root-checkbox-sonar"
              >
                <Checkbox
                  checked={sonarFlag}
                  style={{ marginRight: 10 }}
                  disabled={!isEdit}
                  fieldid={`sonar_checkbox`}
                  onChange={handleCheck("sonarFlag")}
                >
                  {lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C80BFF", "启用静态扫描") /* "启用静态扫描" */}
                </Checkbox>
              </FormItem>
              {
                sonarFlag ?
                  <FormItem label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C80BFB", "语言类型：") /* "语言类型：" */}>
                    <Select fieldid="newFlow_HXtV5iwEd4_select"
                      multiple
                      disabled={!isEdit}
                      value={state.sonarLanguageType}
                      style={{ width: "200px" }}
                      searchPlaceholder={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_17EC411004C80BFA", "语言类型") /* "语言类型" */}
                      scrollToEnd={scrollToEnd}
                      onChange={handleChange("sonarLanguageType")}
                    >
                      {
                        comboLanguageTypeData.map(item => (
                          <Option key={item.value} value={item.value} >{item.name}</Option>
                        ))
                      }
                    </Select>
                  </FormItem>
                  : null
              }
            </Col>
          </Row>
        </div>
      </div>
    </div>
  );
});

export default inject('CreateProcessStore')(observer(Sonar));
