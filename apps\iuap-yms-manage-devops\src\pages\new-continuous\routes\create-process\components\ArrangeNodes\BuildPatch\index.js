import React, { useState, useEffect, useRef, useCallback, useImperativeHandle, forwardRef, useMemo } from 'react';
import { Button, Tag, DataForm, Checkbox, Collapse, DataTable, Switch } from 'iuap-gp-ymscloudui-fe/baseui';
import { toJS } from 'mobx';
import { inject, observer } from 'mobx-react';
import { err, warn, MessageInline, HelpTip } from "iuap-gp-ymscloudui-fe/components";
import { getDC, getYmsEnvByDC, getPatchSpecial, checkPatchName, getPatchBuildNodeInfo, queryRuleDataType } from 'newDevopsService/NewService'
import AddPluginsModal from "./AddPluginsModal";

import './index.less';

const { Panel } = Collapse;
const { Item } = DataForm;

const BuildPatch = forwardRef((props, ref) => {
  const { CreateProcessStore, taskCode, taskinfo, devopsType, isEdit, appCode, applicationCode, id } = props;
  const { pipelineStages } = CreateProcessStore;

  const form = useRef(null);
  const flag = useRef(true);
  const specialOptionsCache = useRef({});
  const autoMatchBaseLine = useRef(false);

  const executeNew = useMemo(() => taskCode === "buildPatchV2", [taskCode]);

  const { workSpaceArr, gitBranch } = useMemo(() => {
    const workSpaceArr = [];
    let gitBranch = '';
    pipelineStages.forEach(item => {
      item.taskList.forEach(it => {
        if (it.firstNode === true) {
          workSpaceArr.push(it.taskinfo.params.workSpace)
        }
        if (it.code === "gitCode") {
          gitBranch = it.taskinfo.params.revision;
        }
      })
    });
    return { workSpaceArr, gitBranch };
  }, [pipelineStages]);

  const [patchUsageInfoId, setPatchUsageInfoId] = useState(2);
  const [modalVisible, setModalVisible] = useState(false);
  const [yprPlugins, setYprPlugins] = useState([]);
  const [ymsDisabled, setYmsDisabled] = useState(false);
  const [autoPatch, setAutoPatch] = useState(false);
  const [autoPathInfos, setAutoPathInfos] = useState();
  const [isSpecial, setIsSpecial] = useState(false);
  const [isYmsConfig, setIsYmsConfig] = useState(false);
  const [isJQType, setIsJQType] = useState(false);
  const [productVersion, setProductVersion] = useState('');
  const [showVersion, setShowVersion] = useState('');
  const [dependentInfo, setDependentInfo] = useState([]);
  const [showVersionOptions, setShowVersionOptions] = useState([]);
  const [productCodeOptions, setProductCodeOptions] = useState([]);
  const [productCode, setProductCode] = useState('');
  const [versionTypeOptions, setVersionTypeOptions] = useState([]);
  const [versionType, setVersionType] = useState('');
  const [serialNumOptions, setSerialNumOptions] = useState([]);
  const [patchBaselineSerialNum, setPatchBaselineSerialNum] = useState('');
  const [isCustomVersion, setIsCustomVersion] = useState(false);
  const [productName, setProductName] = useState('');
  const [patchBaseLine, setPatchBaseLine] = useState('');
  const [templateId, setTemplateId] = useState('');
  const [specialOptions, setSpecialOptions] = useState([]);
  const [specialProjectName, setSpecialProjectName] = useState('');
  const [dcList, setDcList] = useState([]);
  const [ymsEnvList, setYmsEnvList] = useState([]);
  const [ymsConfigDcCode, setYmsConfigDcCode] = useState('');
  const [ymsConfigDcName, setYmsConfigDcName] = useState('');
  const [ymsConfigEnvName, setYmsConfigEnvName] = useState('');
  const [workSpace, setWorkSpace] = useState(workSpaceArr[0] || 'sourceWorkSpace');

  const getBaseInfo = useCallback(async () => {
    const params = {
      msoCode: (id === 'new' ? appCode : applicationCode) || 'undefined',
      pipelineCode: appCode,
      branch: gitBranch
    };
    if (!isEdit) {
      return
    }
    const res = await getPatchBuildNodeInfo(params, executeNew);
    autoMatchBaseLine.current = executeNew && res.length === 1 && res[0].matchFlag ? true : false;
    setShowVersionOptions(res.map(it => ({ ...it, name: 'showVersion', key: it.showVersion, value: it.showVersion, label: it.showVersion })));
    return res;
  }, [appCode, applicationCode, isEdit, id, gitBranch, executeNew]);

  const getYmsEnvList = useCallback((region, updateEnv) => {
    getYmsEnvByDC({ region }).then(data => {
      if (data) {
        if (updateEnv) {
          form.current.setFieldsValue({ ymsConfigEnvCode: data[0]?.ymsEnv });
        }
        setYmsEnvList(data)
      } else {
        err(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1BEFD95C04980146", "获取yms环境列表出错") /* "获取yms环境列表出错" */)
      }
    })
  }, []);

  const getDcList = useCallback(() => {
    if (!isYmsConfig || (dcList && dcList.length > 0)) {
      return
    }
    getDC().then(data => {
      if (data) {
        setDcList(data);
        const ymsConfigDcCode = form.current.getFieldValue('ymsConfigDcCode');
        if (ymsConfigDcCode) {
          getYmsEnvList(ymsConfigDcCode);
        }
      } else {
        err(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1BEFD95C04980144", "获取数据中心出错") /* "获取数据中心出错" */)
      }
    })
  }, [isYmsConfig, dcList, getYmsEnvList]);

  const getSpecialOptions = useCallback(() => {
    let cachedOptions = specialOptionsCache.current[showVersion];
    if (!isSpecial || !showVersion) {
      return
    }
    if (cachedOptions) {
      setSpecialOptions(cachedOptions);
      return
    }
    getPatchSpecial({ showVersion }).then(data => {
      const options = data.map(item => {
        return {
          key: item.id,
          label: item.environmentName,
          value: item.id,
        }
      })
      specialOptionsCache.current[showVersion] = options;
      setSpecialOptions(options);
    })
  }, [isSpecial, showVersion]);

  const handleSelect = useCallback((value, option = {}, allValues = {}) => {
    console.log(value, option);
    if (option.name === 'showVersion') {
      const productCodeOptions = option.productLists?.map(it => ({ ...it, name: 'productCode', key: it.productCode, value: it.productCode, label: it.productName })) || [];
      const nextNode = productCodeOptions.find(it => it.value === allValues.productCode) || productCodeOptions[0];
      setShowVersion(value);
      setProductCodeOptions(productCodeOptions);
      form.current.setFieldsValue({ showVersion: value });
      if (nextNode) {
        handleSelect(nextNode.value, { ...nextNode, name: 'productCode' }, allValues);
      }
    } else if (option.name === 'productCode') {
      let versionTypeOptions = option.versionTypes?.map(it => ({ ...it, name: 'versionType', key: it.versionType, value: String(it.versionType), label: it.versionType === null ? 'RELEASE' : it.versionType })) || [];
      if (versionTypeOptions.find(it => it.custom === true)) {
        versionTypeOptions = [{ ...versionTypeOptions[0], label: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B0052800CC", "自定义") /* "自定义" */, value: 'isCustomVersion' }, ...versionTypeOptions]
      }
      const nextNode = versionTypeOptions.find(it => it.value === allValues.versionType) || versionTypeOptions[0];
      setProductCode(value);
      setVersionTypeOptions(versionTypeOptions);
      form.current.setFieldsValue({ productCode: value });
      if (nextNode) {
        handleSelect(nextNode.value, { ...nextNode, name: 'versionType' }, allValues);
      }
    } else if (option.name === 'versionType') {
      const serialNumOptions = option.iyprqPatchVOS?.map(it => ({ ...it, name: 'patchBaselineSerialNum', key: it.patchBaselineSerialNum, value: it.patchBaselineSerialNum, label: it.patchBaselineSerialNum })) || [];
      const nextNode = serialNumOptions.find(it => it.value === allValues.patchBaselineSerialNum) || serialNumOptions[0];
      setVersionType(option.versionType);
      setSerialNumOptions(serialNumOptions);
      setIsCustomVersion(value === 'isCustomVersion');
      form.current.setFieldsValue({ versionType: value });
      if (nextNode) {
        handleSelect(nextNode.patchBaselineSerialNum, { ...nextNode, name: 'patchBaselineSerialNum' }, allValues)
      }
    } else if (option.name === 'patchBaselineSerialNum') {
      const { productName, patchTypeName, patchBaselineVersion, productVersion, patchBaseLine, deployModelGitBranch, templateId, productShowVersion } = option;
      if (executeNew && templateId) {
        // 从制品规范里获取 YMS 配置
        const parts = productShowVersion.split('.');
        const endVersion = parts.length > 2 ? parts[2] : productShowVersion;
        queryRuleDataType({ templateId, endVersion, dataType: 10 }).then(data => {
          if (data) {
            data.dcCode !== form.current.getFieldValue('data.ymsConfigDcCode') && getYmsEnvList(data.dcCode, false);
            form.current.setFieldsValue({ ymsConfigDcCode: data.dcCode, ymsConfigEnvCode: data.ymsEnv });
            if (data.dcCode && data.ymsEnv) {
              setYmsDisabled(true);
            }
          } else {
            setYmsDisabled(false);
          }
        });
      }
      setPatchBaselineSerialNum(value);
      setProductName(productName);
      setProductVersion(productVersion);
      setPatchBaseLine(patchBaseLine);
      setTemplateId(templateId);
      form.current.setFieldsValue({
        patchBaselineSerialNum: value,
        productName,
        patchTypeName,
        patchBaselineVersion,
        deployModelGitBranch,
      });
    }
  }, [executeNew, getYmsEnvList]);

  useEffect(() => {
    const mountLogic = async () => {
      const params = toJS(taskinfo.params);
      const { isSpecial = false, isYmsConfig = false, productVersion, patchUsageInfoId, yprPlugins, dependentInfo, autoPatch, productCode, productName, patchBaselineSerialNum, autoPathInfos } = params;

      let fetchedOptions;
      if (!autoPatch) {
        fetchedOptions = await getBaseInfo();
      }

      let { showVersion: pvShowVersion, versionType: pvVersionType, patchBaselineVersion } = params;
      if (productVersion && productVersion.split('-').length === 2) {
        pvShowVersion = pvShowVersion || patchBaselineVersion;
        pvVersionType = pvVersionType || productVersion.split('-')[1];
      } else if (productVersion?.split('-').length === 1) {
        pvShowVersion = pvShowVersion || productVersion;
        pvVersionType = pvVersionType || 'RELEASE';
      }

      setAutoPatch(autoPatch);
      setAutoPathInfos(autoPathInfos);
      setIsSpecial(isSpecial);
      setIsYmsConfig(isYmsConfig);
      setIsJQType(devopsType === 'jq');
      setProductVersion(productVersion);
      setShowVersion(pvShowVersion);
      setPatchUsageInfoId(patchUsageInfoId);
      setYprPlugins(yprPlugins || []);
      setDependentInfo(dependentInfo ? JSON.parse(dependentInfo) : []);

      if (isEdit) {
        const options = showVersionOptions.length > 0 ? showVersionOptions : (fetchedOptions || []).map(it => ({ ...it, name: 'showVersion', key: it.showVersion, value: it.showVersion, label: it.showVersion }));
        const selectedOption = pvShowVersion && options?.find(it => it.showVersion === pvShowVersion);
        if (!autoPatch && selectedOption) {
          handleSelect(
            pvShowVersion,
            { ...selectedOption, name: 'showVersion' },
            { showVersion: pvShowVersion, productCode, versionType: pvVersionType, patchBaselineSerialNum }
          );
        } else if (executeNew) {
          const firstOption = options?.[0];
          firstOption && handleSelect(
            firstOption.showVersion,
            { ...firstOption, name: 'showVersion' }
          );
        }
      }
      getDcList();
      form.current.setFieldsValue({
        ...params,
        showVersion: pvShowVersion,
        versionType: pvVersionType,
        productCode: (isEdit || autoPatch || !productName) ? productCode : productName,
        isSpecial,
        isYmsConfig,
      });
    };
    mountLogic();
  }, []);


  useEffect(() => {
    getSpecialOptions();
    const specialProjectId = form.current?.getFieldValue('specialProjectId');
    if (specialProjectId && specialOptions && !specialOptions.find(item => item.value === specialProjectId)) {
      form.current.setFieldsValue({ specialProjectId: undefined });
      setSpecialProjectName('');
    }
  }, [showVersion, specialOptions, getSpecialOptions]);


  const onFieldsChange = useCallback((value, allFields) => {
    console.log(value, allFields);
    const key = Object.keys(value)[0];
    const val = value[key];
    switch (key) {
      case "isSpecial":
        setIsSpecial(val);
        getSpecialOptions();
        break;

      case "isYmsConfig":
        setIsYmsConfig(val);
        getDcList();
        break;

      case "autoPatch":
        setAutoPatch(val);
        if (!val && showVersionOptions === undefined) {
          getBaseInfo();
        }
        break;

      case "patchUsageInfoId":
        setPatchUsageInfoId(val);
        break;

      default:
        break;
    }
  }, [getSpecialOptions, getDcList, getBaseInfo, showVersionOptions]);

  const checkDependencePatch = (_rule, val, callback) => {
    if (!val) {
      return callback();
    }
    if (val.length > 500) {
      return callback(lang.templateByUuid('UID:P_GPAASYPR-FE_17B43C0005F80576', '依赖补丁编码的长度要小于等于500'))
    }
    checkPatchName({ patchName: val }).then(res => {
      callback();
    }).catch((err => {
      callback(err.detailMsg);
    }));
  };

  const savePlugings = (data) => {
    const addedPlugins = data.map(rec => {
      const { name, code, version } = rec;
      return {
        pluginName: name,
        pluginCode: code,
        pluginVersion: version,
      }
    });
    setYprPlugins(prevPlugins => prevPlugins.concat(addedPlugins));
  };

  const saveData = async () => {
    const sourceCodeType = pipelineStages[0].taskList[0].code;
    if (sourceCodeType !== 'gitCode') {
      flag.current = false;
      return warn(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1DA9469605900032", "仅代码源方式支持添加制作补丁") /* "仅代码源方式支持添加制作补丁" */)
    }
    try {
      const values = await form.current.validateFields();
      console.log('formValues', values);
      let { updateNodeInfo } = CreateProcessStore;
      let data = { appCode: applicationCode, ...values };
      if (executeNew) {
        data.productTemplateId = templateId;
        data.autoMatchBaseLine = false; // autoMatchBaseLine.current;
        data.isCustomVersion = isCustomVersion;
      }
      if (values.autoPatch) {
        data = { ...values, ymsConfigDcName, ymsConfigEnvName };
      } else {
        const stateData = { specialProjectName, ymsConfigDcName, ymsConfigEnvName, patchBaseLine, productVersion, showVersion, productName, yprPlugins };
        Object.keys(stateData).forEach(item => {
          if (stateData[item] !== undefined) {
            data[item] = stateData[item];
          }
        })
        if (data.versionType !== 'SNAPSHOT') {
          data.latestBaselineSerialNum = false;
        }
        if (!data.isYmsConfig) {
          data.ymsConfigEnvCode = undefined;
          data.ymsConfigDcCode = undefined;
          data.ymsConfigDcName = undefined;
          data.ymsConfigEnvName = undefined;
        }
      }
      flag.current = true;
      updateNodeInfo(data);
    } catch (errorInfo) {
      flag.current = false;
    }
  };

  useImperativeHandle(ref, () => ({
    saveData,
    flag: flag.current,
  }));

  const columns = useMemo(() => [
    {
      title: lang.templateByUuid('UID:P_GPAASDEVOPS-FE_1A13293A05D80152', '插件名称') /* "插件名称" */,
      dataIndex: 'pluginName',
      key: 'pluginName',
      width: "35%",
      render: (text, rec) => {
        let name = text;
        if (rec.installTypes && Array.isArray(rec.installTypes)) {
          rec.installTypes.forEach((it, index) => {
            if (it.code != "standard") {
              name = name + `-${it.type}`
            }
          })
        }
        return name
      }
    },
    {
      title: lang.templateByUuid('UID:P_GPAASDEVOPS-FE_1A13293A05D80153', '插件编码') /* "插件编码" */,
      dataIndex: 'pluginCode',
      key: 'pluginCode',
      width: "35%",
    },
    {
      title: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1A13293A05D80035", "最新版本") /* "最新版本" */,
      dataIndex: 'pluginVersion',
      key: 'pluginVersion',
      width: "20%",
      render: (text, rec) => {
        return rec.versionType && rec.versionType !== "null" ? `${text ? text : rec.version}-${rec.versionType}` : (text ? text : rec.version)
      }
    },
  ], []);

  const header = (name, href) => {
    return <div>
      <div id={href} className={href + ' config-panel'} />
      <div>{name}</div>
    </div>
  }

  return (
    <div className='build-patch-container'>
      <DataForm
        formLayout={2}
        labelWidth={120}
        ref={form}
        initialValues={{
          patchUsageInfoId: 2
        }}
        onValuesChange={onFieldsChange}
        disabled={!isEdit}
      >
        {executeNew ? null : <div style={{ padding: 16 }}>
          <Item inputType={'custom'} valuePropName={'checked'} label=" " htmlFor={null} name='autoPatch' colSpan={24}>
            <Checkbox>
              {lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B0052800CF", "自动制作补丁") /* "自动制作补丁" */}
              <HelpTip placement="right"
                msg={<span>
                  <p>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B0052800D0", "勾选自动制作补丁后，会根据当前分支自动为对应版本的产品盘制作补丁；") /* "勾选自动制作补丁后，会根据当前分支自动为对应版本的产品盘制作补丁；" */}</p>
                  <p>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B0052800D1", "仅支持为R5_517SP及以后的已发布版本和在研状态的自定义版本自动制作补丁，且分支必须符对应版本的分支规范；") /* "仅支持为R5_517SP及以后的已发布版本和在研状态的自定义版本自动制作补丁，且分支必须符对应版本的分支规范；" */}</p>
                </span>}
              />
            </Checkbox>
          </Item>
          {autoPatch && <>
            <Item inputType={'custom'} valuePropName={'checked'} label=" " htmlFor={null} name='isYmsConfig' colSpan={24}>
              <Checkbox>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B8043801A0", "下载YMS配置") /* "下载YMS配置" */}</Checkbox>
            </Item>
            {
              !!isYmsConfig && <Item htmlFor={null} inputType={'select'}
                name={'ymsConfigDcCode'}
                label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B8043801A7", "数据中心") /* "数据中心" */}
                options={dcList.map(dc => ({ key: dc.region, value: dc.region, label: dc.name }))}
                onChange={(val, option) => {
                  setYmsConfigDcCode(val);
                  setYmsConfigDcName(option.label);
                  getYmsEnvList(val, true);
                }}
              />
            }
            {
              !!isYmsConfig && <Item htmlFor={null} inputType={'select'}
                name={'ymsConfigEnvCode'}
                label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380192", "环境") /* "环境" */}
                options={ymsEnvList.map(env => ({ key: env.ymsEnv, value: env.ymsEnv, label: env.ymsName }))}
                onChange={(val, option) => {
                  setYmsConfigEnvName(option.label);
                }}
              />
            }
          </>}
        </div>}
        {!autoPatch && <Collapse type='list' className='config-content' ghost={false}>
          <Panel defaultExpanded key={1} header={header(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380193", "产品盘信息") /* "产品盘信息" */, 'product')} >
            <Item htmlFor={null} inputType={'select'}
              disabled={autoMatchBaseLine.current}
              name={'showVersion'}
              label={"YPR" + lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B0052800D3", "版本") /* "版本" */}
              options={showVersionOptions}
              onChange={handleSelect}
              rules={[
                {
                  required: true,
                  message: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B0052800D6", "请选择版本") /* "请选择版本" */
                }
              ]}
            />
            <Item htmlFor={null} inputType={'select'}
              disabled={autoMatchBaseLine.current}
              name={'productCode'}
              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380197", "产品盘名称") /* "产品盘名称" */}
              options={productCodeOptions}
              onChange={handleSelect}
              rules={[
                {
                  required: true,
                  message: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B0052800CE", "请选择产品盘") /* "请选择产品盘" */
                }
              ]}
            />
            <Item inputType={'input'} disabled
              name={'productCode'}
              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B8043801A2", "产品盘编码") /* "产品盘编码" */}
              required
            />
            <Item htmlFor={null} inputType={'select'}
              name={'versionType'}
              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B0052800D2", "发布版本") /* "发布版本" */}
              options={versionTypeOptions}
              onChange={handleSelect}
            />
            {
              isJQType &&
              <Item htmlFor={null} inputType={'select'}
                name={'workSpace'}
                label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_18F928C405080115", "工作空间")}
                options={workSpaceArr.map((item, index) => { return { key: index, label: item, value: item } })}
                onChange={(value) => {
                  setWorkSpace(value);
                }}
                rules={[
                  {
                    required: true,
                    message: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_18F928C405080113", "请选择工作空间") /* "请选择工作空间" */
                  }
                ]}
              />
            }
          </Panel>
          <Panel defaultExpanded key={2} header={header(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380191", "补丁信息") /* "补丁信息" */, 'patch')} >
            <Item htmlFor={null} inputType={'select'}
              disabled={autoMatchBaseLine.current}
              name={'patchBaselineSerialNum'}
              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B8043801A4", "安装盘/SP流水号") /* "安装盘/SP流水号" */}
              options={serialNumOptions}
              onChange={handleSelect}
              rules={[
                {
                  required: true,
                  message: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B0052800D4", "请选择流水号") /* "请选择流水号" */
                }
              ]}
            />
            {versionType === 'SNAPSHOT' && <Item inputType={'custom'} valuePropName={'checked'} htmlFor={null}
              name='latestBaselineSerialNum'
              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B0052800CA", "最新") /* "最新" */}
              tooltip={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1FECD2B0052800CB", "勾选后，每次执行会使用最新构建记录制作补丁") /* "勾选后，每次执行会使用最新构建记录制作补丁" */}
            >
              <Checkbox />
            </Item>}
            <Item inputType={'input'} disabled
              name={'patchTypeName'}
              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380196", "补丁类型") /* "补丁类型" */}
              required
            />
            <Item inputType={'input'} disabled
              name={'patchBaselineVersion'}
              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B80438019F", "补丁基线") /* "补丁基线" */}
            />
            <Item inputType={'input'} disabled
              name={'deployModelGitBranch'}
              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D86E07405B001C1", "安装模式") /* "安装模式" */}
            />
            {!isEdit && <Item
              inputType="radiogroup"
              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D86E07405B001BA", "是否专项") /* "是否专项" */}
              name="isSpecial"
              options={[
                {
                  label: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D86E07405B001BC", "是") /* "是" */,
                  value: true
                },
                {
                  label: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D86E07405B001BF", "否") /* "否" */,
                  value: false
                },
              ]}
            />}
            {isEdit ? null : isSpecial ? <Item htmlFor={null} inputType={'select'}
              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D86E07405B001B9", "专项名称") /* "专项名称" */}
              name="specialProjectId"
              options={specialOptions}
              onChange={(value, option) => {
                setSpecialProjectName(option.label);
              }}
            /> : <Item htmlFor={null} inputType={'select'} disabled
              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B80438019B", "补丁用途") /* "补丁用途" */}
              name="patchUsageInfoId"
              options={[
                { value: 2, label: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D86E07405B001C0", "内部验证（不允许发布）", undefined, { returnStr: true }) /* "内部验证（不允许发布）" */ },
                { value: 3, label: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D86E07405B001C2", "紧急补丁", undefined, { returnStr: true }) /* "紧急补丁" */ }
              ]}
            />}
            <Item inputType={'input'}
              name={'dependencyPatchNames'}
              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380194", "依赖补丁") /* "依赖补丁" */}
              rules={[
                {
                  validator: checkDependencePatch,
                }
              ]}
            />
          </Panel>
          <Panel defaultExpanded key={3} header={header(lang.templateByUuid('UID:P_GPAASDEVOPS-FE_1A13293A05D801B3', '配置') /* "配置" */, 'yms')} >
            <Item inputType={'custom'} valuePropName={'checked'} label=" " htmlFor={null} name='isYmsConfig' colSpan={24}>
              <Checkbox>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B8043801A0", "下载YMS配置") /* "下载YMS配置" */}</Checkbox>
            </Item>
            {
              !!isYmsConfig && <Item htmlFor={null} inputType={'select'}
                name={'ymsConfigDcCode'}
                label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B8043801A7", "数据中心") /* "数据中心" */}
                disabled={ymsDisabled}
                options={dcList.map(dc => ({ key: dc.region, value: dc.region, label: dc.name }))}
                onChange={(val, option) => {
                  setYmsConfigDcCode(val);
                  setYmsConfigDcName(option.label);
                  getYmsEnvList(val, true);
                }}
              />
            }
            {
              !!isYmsConfig && <Item htmlFor={null} inputType={'select'}
                name={'ymsConfigEnvCode'}
                label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380192", "环境") /* "环境" */}
                disabled={ymsDisabled}
                options={ymsEnvList.map(env => ({ key: env.ymsEnv, value: env.ymsEnv, label: env.ymsName }))}
                onChange={(val, option) => {
                  setYmsConfigEnvName(option.label);
                }}
              />
            }
            <Item inputType={'custom'} valuePropName={'checked'} label=" " htmlFor={null} name='isAttachImage'>
              <Checkbox>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D86E07405B001C3", "携带镜像") /* "携带镜像" */}</Checkbox>
            </Item>
            <Item inputType={'custom'} valuePropName={'checked'} label=" " htmlFor={null} name='isClearRedisCache'>
              <Checkbox>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D86E07405B001BB", "清理Redis缓存") /* "清理Redis缓存" */}</Checkbox>
            </Item>
            <Item inputType={'custom'} valuePropName={'checked'} label=" " htmlFor={null} name='isDBPatch'>
              <Checkbox>{lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D86E07405B001BE", "DB补丁") /* "DB补丁" */}</Checkbox>
            </Item>
          </Panel>
          {
            (isEdit || yprPlugins.length > 0) ? (
              <Panel defaultExpanded key={4} header={header(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1DA9469605900031", "插件") /* "插件" */, 'plugins')} className={'add-plugins-panel'}>
                <div style={{ height: 300 }}>
                  <DataTable
                    columns={columns}
                    data={yprPlugins}
                    renderToolBar={() => {
                      return (
                        <div style={{ display: 'flex', 'justifyContent': 'end' }}>
                          <Button colors={'primary'} onClick={() => { setModalVisible(true) }}>
                            {lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1DA9469605900033", "添加插件") /* "添加插件" */}
                          </Button>
                        </div>
                      )
                    }}
                    operationItems={[
                      {
                        key: "del",
                        text: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1B0548CC05D00092", "删除") /* "删除" */,
                        onClick: (item) => {
                          console.log(item);
                          setYprPlugins(yprPlugins.filter(p => !(p.pluginCode === item.pluginCode && p.pluginVersion === item.pluginVersion)))
                        },
                      }
                    ]}
                  />
                </div>
                {
                  modalVisible && <AddPluginsModal
                    visible={modalVisible}
                    setVisible={(v) => { setModalVisible(v) }}
                    savePlugings={savePlugings}
                    appCode={yprPlugins.map(item => item.pluginCode).join(',')}
                    executeNew={executeNew}
                  />
                }
              </Panel>
            ) : null
          }
          {
            (!isEdit && dependentInfo) ? (
              <Panel defaultExpanded key={5} header={header(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B80438019D", "关联Jira") /* "关联Jira" */, 'jira')} >
                {
                  dependentInfo.map((item, index) => {
                    return (<div className={'jira-info'} key={index}>
                      <Item inputType={'input'} disabled
                        label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380065", "Jira编号") /* "Jira编号" */}
                        required
                        value={item.code}
                      />
                      {item.dspCode ? (
                        <Item inputType={'input'} disabled
                          label={lang.templateByUuid("UID:P_GPAASYPR-FE_1CF83C9404280092", "SOP单据号") /* "SOP单据号" */}
                          value={item.dspCode}
                        />
                      ) : null}
                      <Item inputType={'input'} disabled
                        label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380069", "问题描述") /* "问题描述" */}
                        required
                        value={item.description}
                      />
                      <Item inputType={'input'} disabled
                        label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380064", "解决方案") /* "解决方案" */}
                        value={item.solution}
                      />
                    </div>)
                  })
                }
              </Panel>
            ) : null
          }
        </Collapse>}
      </DataForm >
      {
        !isEdit && autoPathInfos?.map((item, index) => {
          return <div className='patch-info-item' key={index}>
            <DataForm formLayout={2} disabled labelWidth={120} initialValues={item}>
              <Collapse type='list' className='config-content' ghost={false}>
                <Panel defaultExpanded key={1} header={header(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380193", "产品盘信息") /* "产品盘信息" */, 'product')} >
                  <Item htmlFor={null} inputType={'select'} colSpan={24}
                    name={'productVersion'}
                    label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B8043801AA", "产品盘版本") /* "产品盘版本" */}
                  />
                  <Item inputType={'input'}
                    name={'productName'}
                    label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380197", "产品盘名称") /* "产品盘名称" */}
                    required
                  />
                  <Item inputType={'input'}
                    name={'productCode'}
                    label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B8043801A2", "产品盘编码") /* "产品盘编码" */}
                    required
                  />
                </Panel>
                <Panel defaultExpanded key={2} header={header(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380191", "补丁信息") /* "补丁信息" */, 'patch')} >
                  <Item inputType={'input'}
                    name={'patchTypeName'}
                    label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380196", "补丁类型") /* "补丁类型" */}
                    required
                  />
                  <Item inputType={'input'}
                    name={'patchBaselineVersion'}
                    label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B80438019F", "补丁基线") /* "补丁基线" */}
                  />
                  <Item inputType={'input'}
                    name={'patchBaselineSerialNum'}
                    label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B8043801A4", "安装盘/SP流水号") /* "安装盘/SP流水号" */}
                    prefix='#'
                    required
                  />
                  <Item inputType={'input'}
                    name={'deployModelGitBranch'}
                    label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D86E07405B001C1", "安装模式") /* "安装模式" */}
                  />
                  <Item
                    inputType="radiogroup"
                    label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D86E07405B001BA", "是否专项") /* "是否专项" */}
                    name="isSpecial"
                    options={[
                      {
                        label: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D86E07405B001BC", "是") /* "是" */,
                        value: true
                      },
                      {
                        label: lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1D86E07405B001BF", "否") /* "否" */,
                        value: false
                      },
                    ]}
                  />
                  <Item inputType={'input'}
                    name={'dependencyPatchNames'}
                    label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380194", "依赖补丁") /* "依赖补丁" */}
                  />
                </Panel>
                {
                  item.dependentInfo ? (
                    <Panel defaultExpanded key={5} header={header(lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B80438019D", "关联Jira") /* "关联Jira" */, 'jira')} >
                      {
                        JSON.parse(item.dependentInfo)?.map((depItem, depIndex) => {
                          return (<div className={'jira-info'} key={depIndex}>
                            <Item inputType={'input'} disabled
                              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380065", "Jira编号") /* "Jira编号" */}
                              required
                              value={depItem.code}
                            />
                            {depItem.dspCode ? (
                              <Item inputType={'input'} disabled
                                label={lang.templateByUuid("UID:P_GPAASYPR-FE_1CF83C9404280092", "SOP单据号") /* "SOP单据号" */}
                                value={depItem.dspCode}
                              />
                            ) : null}
                            <Item inputType={'input'} disabled
                              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380069", "问题描述") /* "问题描述" */}
                              required
                              value={depItem.description}
                            />
                            <Item inputType={'input'} disabled
                              label={lang.templateByUuid("UID:P_GPAASDEVOPS-FE_1CA937B804380064", "解决方案") /* "解决方案" */}
                              value={depItem.solution}
                            />
                          </div>)
                        })
                      }
                    </Panel>
                  ) : null
                }
              </Collapse>
            </DataForm>
          </div>
        })
      }
    </div >
  )
});

export default inject('CreateProcessStore')(observer(BuildPatch));